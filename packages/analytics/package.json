{"name": "@qantasexperiences/analytics", "private": false, "version": "1.46.0", "type": "module", "sideEffects": false, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"imports": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "license": "MIT", "scripts": {"build": "tsup", "dev": "pm2 start --name analytics \"tsup --watch\"", "delete": "pm2 delete analytics", "clean": "rm -rf .turbo node_modules dist", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "dependencies": {"@qantasexperiences/utils": "workspace:*", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "lodash": "^4.17.21", "omit-empty-es": "^1.2.0", "zod": "^3.25.67"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@qantasexperiences/white-label": "workspace:*", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.19", "jest": "^30.0.5", "pm2": "^6.0.8", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}, "tsup": {"entry": ["src/index.tsx"], "clean": true, "dts": true, "format": ["cjs", "esm"], "external": ["react", "next"]}}