# Change Log - @qantasexperiences/analytics

<!-- This log was last generated on Thu, 14 Aug 2025 05:28:40 GMT and should not be manually modified. -->

<!-- Start content -->

## 1.46.0

Thu, 14 Aug 2025 05:28:40 GMT

### Minor changes

- feat: add in strikethrough data to event data as per analytics request (<EMAIL>)

## 1.45.0

Wed, 13 Aug 2025 05:12:04 GMT

### Minor changes

- Revert "chore: modify createHotelsOptimizelyFeatureDecisionEventPayload (#2017)" (<EMAIL>)

## 1.44.0

Tu<PERSON>, 12 Aug 2025 00:23:41 GMT

### Minor changes

- chore: modify createHotelsOptimizelyFeatureDecisionEventPayload structure (<EMAIL>)

## 1.43.0

Thu, 07 Aug 2025 02:39:27 GMT

### Minor changes

- added tab click event payload creation (<EMAIL>)

## 1.42.0

<PERSON><PERSON>, 29 Jul 2025 03:59:16 GMT

### Minor changes

- login logout event payload creators (<EMAIL>)

## 1.41.2

<PERSON><PERSON>, 29 Jul 2025 03:28:34 GMT

### Patches

- chore: upgrade @testing-library packages (<EMAIL>)

## 1.41.1

Mon, 28 Jul 2025 00:21:49 GMT

### Patches

- chore: update jest (<EMAIL>)

## 1.41.0

Mon, 21 Jul 2025 00:52:53 GMT

### Minor changes

- feat: updated tests to include the actionType (<EMAIL>)

## 1.40.0

Thu, 03 Jul 2025 04:23:08 GMT

### Minor changes

- feat: add strikethrough data to hotels analytic events (<EMAIL>)

## 1.39.1

Wed, 02 Jul 2025 05:30:37 GMT

### Patches

- feat: add view_search_results event (for holidays) (<EMAIL>)

## 1.39.0

Wed, 02 Jul 2025 04:16:38 GMT

### Minor changes

- remove source field from createHotelsOptimizelyFeatureDecisionEventPayload (<EMAIL>)

## 1.38.0

Tue, 01 Jul 2025 03:48:49 GMT

### Minor changes

- feat(ga4): add modal close event (<EMAIL>)

## 1.37.0

Tue, 01 Jul 2025 00:55:55 GMT

### Minor changes

- feat: add ga event for search_submit (holidays) (<EMAIL>)

## 1.36.0

Tue, 01 Jul 2025 00:06:59 GMT

### Minor changes

- fix: fix typo - CLick to Click (<EMAIL>)

## 1.35.1

Mon, 30 Jun 2025 02:09:53 GMT

### Patches

- feat: add sendHolidaysViewItemListEvent (<EMAIL>)

## 1.35.0

Fri, 27 Jun 2025 07:45:44 GMT

### Minor changes

- feat: added map_location_click enums (<EMAIL>)

## 1.34.0

Fri, 27 Jun 2025 06:54:14 GMT

### Minor changes

- updated event creator name (<EMAIL>)

## 1.33.0

Fri, 27 Jun 2025 05:45:43 GMT

### Minor changes

- feat: adding createTileClickEvent (<EMAIL>)

## 1.32.0

Wed, 25 Jun 2025 07:30:54 GMT

### Minor changes

- feat(ga4): search zscalar error tracking ga4 event (<EMAIL>)

## 1.31.0

Wed, 25 Jun 2025 05:36:54 GMT

### Minor changes

- chore: add createOptimizelyFeatureDecision and createHotelsOptimizelyFeatureDecisionEventPayload (<EMAIL>)

## 1.30.0

Wed, 25 Jun 2025 04:43:32 GMT

### Minor changes

- feat: new data-helper for filter update event (<EMAIL>)

## 1.29.1

Tue, 24 Jun 2025 01:07:43 GMT

### Patches

- chore(deps): update deps (<EMAIL>)

## 1.29.0

Thu, 19 Jun 2025 07:22:08 GMT

### Minor changes

- cta click for search panel (<EMAIL>)

## 1.28.0

Tue, 17 Jun 2025 03:55:15 GMT

### Minor changes

- fix: include travellers in events for analytics (<EMAIL>)

## 1.27.0

Wed, 11 Jun 2025 02:00:08 GMT

### Minor changes

- fix: omit available rooms data from other hotel events (<EMAIL>)

## 1.26.1

Tue, 10 Jun 2025 06:53:05 GMT

### Patches

- feat: add new event fields and refactor search error (<EMAIL>)

## 1.26.0

Tue, 10 Jun 2025 06:04:57 GMT

### Minor changes

- feat(ga4): initial commit (<EMAIL>)

## 1.25.0

Tue, 03 Jun 2025 07:36:43 GMT

### Minor changes

- feat(ga4): search exit field analytics for desktop inputs implemented (<EMAIL>)

## 1.24.0

Tue, 03 Jun 2025 06:59:30 GMT

### Minor changes

- feat: wrap sort_filter, sort_open, sort_apply and filter_open events in event_data object (<EMAIL>)

## 1.23.1

Mon, 02 Jun 2025 01:37:37 GMT

### Patches

- fix: ctaMessage position into test (<EMAIL>)

## 1.23.0

Fri, 30 May 2025 05:34:34 GMT

### Minor changes

- feat(ga4): created pagination events foundation (<EMAIL>)

## 1.22.0

Fri, 30 May 2025 00:40:08 GMT

### Minor changes

- add form error event (<EMAIL>)

## 1.21.0

Thu, 29 May 2025 02:48:22 GMT

### Minor changes

- feat: create createHotelsViewItemListEventPayload (<EMAIL>)

## 1.20.1

Thu, 29 May 2025 01:57:36 GMT

### Patches

- chore: update dev dependencies (<EMAIL>)

## 1.20.0

Wed, 28 May 2025 07:52:37 GMT

### Minor changes

- feat: update filter apply event with new data and return (<EMAIL>)

## 1.19.1

Wed, 28 May 2025 05:37:37 GMT

### Patches

- fix: update order of fields based on analytics feedback (<EMAIL>)

## 1.19.0

Wed, 28 May 2025 04:15:56 GMT

### Minor changes

- feat: add form complete event (<EMAIL>)

## 1.18.0

Tue, 27 May 2025 03:33:59 GMT

### Minor changes

- feat: add new search error event payload (<EMAIL>)

## 1.17.0

Mon, 26 May 2025 04:42:48 GMT

### Minor changes

- feat: new ga4 analytics event login (<EMAIL>)

## 1.16.0

Mon, 26 May 2025 03:56:02 GMT

### Minor changes

- feat: sendCtaClickEvent for destination pages (<EMAIL>)

## 1.15.0

Mon, 26 May 2025 01:33:24 GMT

### Minor changes

- feat: add new view search results event and create hotels view search results event payload (<EMAIL>)

## 1.14.0

Fri, 23 May 2025 06:58:54 GMT

### Minor changes

- feat: add modal show event (<EMAIL>)

## 1.13.0

Fri, 23 May 2025 05:30:23 GMT

### Minor changes

- feat: create ga4 v2 cta_click event (<EMAIL>)

## 1.12.2

Thu, 22 May 2025 00:04:55 GMT

### Patches

- chore(deps): bump the production-dependencies group with 15 updates (<EMAIL>)

## 1.12.1

Mon, 19 May 2025 04:55:31 GMT

### Patches

- fix: fix incorrect pay in points percent calculations (<EMAIL>)

## 1.12.0

Mon, 19 May 2025 04:13:53 GMT

### Minor changes

- chore: add CreateHotelsSelectItemEvent (<EMAIL>)

## 1.11.0

Mon, 19 May 2025 01:02:47 GMT

### Minor changes

- feat: add createUserPayload and createPageViewEventPayload data helpers (<EMAIL>)

## 1.10.0

Fri, 16 May 2025 05:49:00 GMT

### Minor changes

- fix: refactor hotels begin checkout event (<EMAIL>)

## 1.9.0

Fri, 16 May 2025 01:49:36 GMT

### Minor changes

- added GTM event for footer link click (<EMAIL>)

## 1.8.0

Fri, 16 May 2025 01:25:12 GMT

### Minor changes

- feat: available rooms ga4 for purchase event (<EMAIL>)

## 1.7.0

Thu, 15 May 2025 23:54:32 GMT

### Minor changes

- fix: refactor for null promotion in begin checkout event (<EMAIL>)

## 1.6.0

Thu, 15 May 2025 09:45:19 GMT

### Minor changes

- feat: new ga4 v2 event view item (<EMAIL>)

## 1.5.3

Thu, 15 May 2025 05:05:28 GMT

### Patches

- chore(deps-dev): bump the development-dependencies group across 1 directory with 5 updates (<EMAIL>)

## 1.5.2

Wed, 14 May 2025 01:29:10 GMT

### Patches

- feat: promotion could be null (<EMAIL>)

## 1.5.1

Tue, 13 May 2025 23:59:33 GMT

### Patches

- feat: refine hasOffer condition (<EMAIL>)

## 1.5.0

Tue, 13 May 2025 06:42:50 GMT

### Minor changes

- feat: add data helper createHotelsBeginCheckoutEventPayload (<EMAIL>)

## 1.4.0

Tue, 13 May 2025 06:04:52 GMT

### Minor changes

- feat: export createHotelsAddToCartEvent (<EMAIL>)

## 1.3.0

Tue, 13 May 2025 04:37:58 GMT

### Minor changes

- feat: add new event-creator and data-helper pattern (<EMAIL>)

## 1.2.0

Tue, 13 May 2025 01:23:12 GMT

### Minor changes

- feat: add filter open, select and apply events and data-helpers (<EMAIL>)

## 1.1.4

Wed, 07 May 2025 05:16:29 GMT

### Patches

- Dependency updates. No expected changes. (<EMAIL>)

## 1.1.3

Wed, 07 May 2025 02:05:01 GMT

### Patches

- Dep updates. No change expected. (<EMAIL>)

## 1.1.2

Fri, 02 May 2025 00:02:11 GMT

### Patches

- feat: add new event-creator and data-helper pattern (<EMAIL>)

## 1.1.1

Tue, 29 Apr 2025 06:44:13 GMT

### Patches

- feat: add destination to event_data (<EMAIL>)

## 1.1.0

Tue, 29 Apr 2025 00:13:46 GMT

### Minor changes

- feat: add select_item GTM event (<EMAIL>)

## 1.0.0

Wed, 23 Apr 2025 01:20:07 GMT

### Major changes

- refactor(analytics): remove createEvent abstraction and export events directly (<EMAIL>)

## 0.20.0

Thu, 03 Apr 2025 04:06:58 GMT

### Minor changes

- feat: add missing field page_type to the cta click event (<EMAIL>)

## 0.19.0

Wed, 02 Apr 2025 06:57:46 GMT

### Minor changes

- feat: additional fields required for the tab click GTM event (<EMAIL>)

## 0.18.0

Fri, 28 Mar 2025 00:47:55 GMT

### Minor changes

- feat: add GTM Event to QuickLinks (<EMAIL>)

## 0.17.0

Fri, 28 Mar 2025 00:16:03 GMT

### Minor changes

- feat: add new GTM event type tab-click (<EMAIL>)

## 0.16.0

Wed, 12 Mar 2025 22:08:21 GMT

### Minor changes

- chore: create search-field-exit event (<EMAIL>)

## 0.15.0

Wed, 12 Mar 2025 04:41:24 GMT

### Minor changes

- feat: update enums for relationship page view events and export schemas (<EMAIL>)

## 0.14.0

Wed, 12 Mar 2025 02:39:03 GMT

### Minor changes

- add cta-link event (<EMAIL>)

## 0.13.1

Wed, 05 Mar 2025 05:24:39 GMT

### Patches

- chore: lint error (<EMAIL>)

## 0.13.0

Wed, 05 Mar 2025 02:17:26 GMT

### Minor changes

- chore: bump version (<EMAIL>)
- Adding search submit event when users click on the search button from the activities page (<EMAIL>)

### Patches

- chore: bump packages (<EMAIL>)
- chore: upgrade dependencies (<EMAIL>)

## 0.9.3

Mon, 03 Feb 2025 05:32:37 GMT

### Patches

- chore: version bump analytics package (<EMAIL>)
- chore(deps): bump the production-dependencies group across 1 directory with 15 updates (<EMAIL>)

## 0.9.0

Tue, 28 Jan 2025 03:45:25 GMT

### Minor changes

- feat: publish all required packages (<EMAIL>)

### Patches

- Eslint v9 upgrade. This should not have any impact on consumers, but releasing a patch as source code has been updated with linting fixes. (<EMAIL>)

## 0.8.1

Wed, 11 Dec 2024 07:04:50 GMT

### Patches

- chore(deps-dev): bump the development-dependencies group with 27 updates (<EMAIL>)

## 0.8.0

Tue, 03 Dec 2024 06:03:59 GMT

### Minor changes

- feat: add deposit pay attributes to purchase event (<EMAIL>)

## 0.7.3

Wed, 27 Nov 2024 23:55:20 GMT

### Patches

- fixing linting warnings (<EMAIL>)

## 0.7.2

Sun, 17 Nov 2024 23:02:53 GMT

### Patches

- chore(deps-dev): bump the development-dependencies group with 19 updates (<EMAIL>)

## 0.7.1

Wed, 13 Nov 2024 04:56:22 GMT

### Patches

- chore(deps-dev): bump the development-dependencies group across 1 directory with 29 updates (<EMAIL>)

## 0.7.0

Wed, 13 Nov 2024 01:29:41 GMT

### Minor changes

- noUncheckedIndexedAccess: true (<EMAIL>)

## 0.6.0

Tue, 12 Nov 2024 06:49:25 GMT

### Minor changes

- disable no-unnecessary-condition with FIXMEs (<EMAIL>)

## 0.5.3

Thu, 31 Oct 2024 00:44:10 GMT

### Patches

- chore(deps-dev): bump the development-dependencies group across 1 directory with 10 updates (<EMAIL>)

## 0.5.2

Tue, 15 Oct 2024 04:51:44 GMT

### Patches

- chore(deps-dev): bump the development-dependencies group across 1 directory with 7 updates (<EMAIL>)

## 0.5.1

Mon, 14 Oct 2024 23:33:40 GMT

### Patches

- chore(deps-dev): bump contentful deps (<EMAIL>)

## 0.5.0

Wed, 09 Oct 2024 22:30:54 GMT

### Minor changes

- chore(deps-dev): upgrade typescript (<EMAIL>)

## 0.4.2

Wed, 09 Oct 2024 04:39:54 GMT

### Patches

- chore(deps-dev): bump non-group dependencies (<EMAIL>)

## 0.4.1

Wed, 25 Sep 2024 01:06:43 GMT

### Patches

- fix: build package before release (<EMAIL>)

## 0.4.0

Tue, 24 Sep 2024 05:47:02 GMT

### Minor changes

- chore: change groupName and itemName for Filter Update to reflect UI naming change. (<EMAIL>)

## 0.3.1

Tue, 17 Sep 2024 01:37:39 GMT

### Patches

- fix: pipeline (<EMAIL>)

## 0.3.0

Mon, 16 Sep 2024 23:36:04 GMT

### Minor changes

- chore: remove no more necessary search_start event and add the new search_suggestion_display (<EMAIL>)

### Patches

- fix: pay at property value for purchase event (<EMAIL>)
- feat: automate releases of analytics (<EMAIL>)
- fix schema to include Deals Page filters (<EMAIL>)
