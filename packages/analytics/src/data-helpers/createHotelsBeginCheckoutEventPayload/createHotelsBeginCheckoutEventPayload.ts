import { Decimal } from 'decimal.js';

import type { Payments, PointsConversion, Quote } from '../hotelsUtils/types';
import { ActionType, ComponentType } from '../../enums';
import { cashToPoints } from '../../utils/cash-to-points';
import { getPaymentType } from '../../utils/payment-type';
import {
  calculateDailyPreDiscountAmount,
  calculateDailyRate,
  calculatePointsPaidPercentage,
  calculateTotalNights,
  createLocationString,
  formatDate,
  isPromotionExclusive,
  isPropertyLuxury,
} from '../hotelsUtils';

export interface CreateHotelsBeginCheckoutEventPayloadOptions {
  ctaMessage?: string | null;
  ctaMessageCategory?: string | null;
  isRebooked: boolean;
  payments: Payments;
  pointsConversion: PointsConversion;
  quote: Quote;
}

export interface CreateHotelsBeginCheckoutEventPayloadReturn {
  ecommerce: {
    currency: string;
    items: [
      {
        cash_used: number;
        cta_message?: string | null;
        cta_message_category?: string | null;
        end_date: string;
        exclusive_offer: boolean | undefined;
        has_offer: boolean;
        includes: string;
        index: number;
        international_or_domestic: string;
        item_category: string;
        item_id: string;
        item_name: string;
        item_variant: string;
        location: string;
        luxe: boolean | undefined;
        number_of_nights: number;
        offer_type: string | undefined;
        original_price: number;
        pay_in_points_percent: number;
        payment_type: string;
        points_earned: number;
        points_used: number;
        points_value: number;
        price: number;
        quantity: number;
        rating: number;
        rebook: boolean;
        start_date: string;
        strikethrough_price: boolean;
        travellers_adult: number;
        travellers_children: number;
        travellers_infant: number;
      },
    ];
    value: number;
  };
  event_data: {
    action: ActionType;
    component_type: ComponentType;
    cta_message?: string | null;
    cta_message_category?: string | null;
    strikethrough_price: boolean;
  };
}

export const createHotelsBeginCheckoutEventPayload = ({
  ctaMessage,
  ctaMessageCategory,
  isRebooked,
  payments,
  pointsConversion,
  quote,
}: CreateHotelsBeginCheckoutEventPayloadOptions): CreateHotelsBeginCheckoutEventPayloadReturn => {
  const startDate = formatDate(quote.stay.checkIn);
  const endDate = formatDate(quote.stay.checkOut);

  const totalNights = calculateTotalNights(quote.stay.checkIn, quote.stay.checkOut);

  const { city, state, country } = quote.property.address;

  const location = createLocationString(city, state, country);

  const totalPointsValue = cashToPoints(
    new Decimal(quote.offer.charges.total.amount),
    pointsConversion,
  );

  const hasStrikethroughPrice = Number(quote.offer.charges.strikethrough?.price.amount) > 0;

  return {
    event_data: {
      action: ActionType.START,
      component_type: ComponentType.CHECKOUT,
      ...(ctaMessage && { cta_message: ctaMessage }),
      ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
      strikethrough_price: hasStrikethroughPrice,
    },
    ecommerce: {
      value: Number(quote.offer.charges.total.amount),
      currency: quote.offer.charges.total.currency,
      items: [
        {
          item_id: quote.property.id,
          item_name: quote.property.name,
          item_category: quote.property.category,
          item_variant: quote.roomType.name,
          index: 0,
          quantity: totalNights,
          price: calculateDailyRate(new Decimal(quote.offer.charges.total.amount), totalNights),
          original_price: calculateDailyPreDiscountAmount(
            Number(quote.offer.charges.totalBeforeDiscount.amount),
            totalNights,
          ),
          cash_used: Number(payments.cash.payableNow.amount),
          points_used: Number(payments.points.amount),
          points_value: Number(totalPointsValue),
          points_earned: Number(quote.offer.pointsEarned?.qffPoints.total),
          pay_in_points_percent: calculatePointsPaidPercentage(
            new Decimal(payments.points.amount ?? 0),
            totalPointsValue,
          ),
          rating: quote.property.rating,
          start_date: startDate,
          end_date: endDate,
          travellers_adult: quote.stay.adults,
          travellers_children: quote.stay.children,
          travellers_infant: quote.stay.infants,
          number_of_nights: totalNights,
          includes: quote.roomType.roomTypeFacilities.join(', '),
          payment_type: getPaymentType({
            cash: new Decimal(quote.offer.charges.payableAtProperty.total.amount),
            creditCard: new Decimal(payments.cash.payableNow.amount ?? 0),
            voucher: new Decimal(payments.voucher.amount ?? 0),
            points: new Decimal(payments.points.amount ?? 0),
          }),
          location,
          international_or_domestic: country === 'Australia' ? 'Domestic' : 'International',
          luxe: isPropertyLuxury(quote.offer.promotion?.name ?? undefined),
          exclusive_offer: isPromotionExclusive(quote.offer.promotion?.name ?? undefined),
          rebook: isRebooked,
          has_offer: !!quote.offer.promotion?.name,
          offer_type: quote.offer.promotion?.name ?? undefined,
          strikethrough_price: hasStrikethroughPrice,
          ...(ctaMessage && { cta_message: ctaMessage }),
          ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
        },
      ],
    },
  };
};
