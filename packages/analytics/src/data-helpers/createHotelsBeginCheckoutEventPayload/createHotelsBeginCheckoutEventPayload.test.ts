import { ActionType, ComponentType } from '../../enums';
import { createHotelsBeginCheckoutEventPayload } from './createHotelsBeginCheckoutEventPayload';

describe('createHotelsBeginCheckoutEventPayload', () => {
  const offer = {
    charges: {
      payableAtBooking: {
        discount: { amount: '200', currency: 'AUD' },
      },
      payableAtProperty: {
        total: { amount: '0', currency: 'AUD' },
      },
      total: { amount: '200', currency: 'AUD' },
      totalBeforeDiscount: { amount: '200', currency: 'AUD' },
    },
    name: 'Internet included',
    paidByDeposit: false,
    pointsEarned: {
      qffPoints: {
        total: 1000,
      },
    },
    promotion: {
      name: 'Save10',
      promotionCode: 'discounted-rate',
    },
  };

  const offerNullPromotion = { ...offer, promotion: null };

  const payload = {
    ctaMessage: 'Hu<PERSON>, we only have 1 left!',
    ctaMessageCategory: 'available rooms',
    isRebooked: false,
    payments: {
      cash: {
        payableNow: {
          amount: 200,
        },
      },
      points: {
        amount: 0,
        amountInCash: 0,
      },
      voucher: {
        amount: 0,
        code: '',
      },
    },
    pointsConversion: {
      levels: [
        {
          max: 0,
          min: 250,
          rate: 0.008,
        },
      ] as [{ max: number; min: number; rate: number }],
      name: 'V1',
    },
    quote: {
      offer,
      property: {
        address: { city: 'Sydney', country: 'Australia', state: 'NSW' },

        category: 'hotel',
        id: '12345',
        name: 'Hotel XYZ',
        rating: 4.5,
      },
      roomType: { name: 'room1', roomTypeFacilities: ['breakfast', 'late check-in', 'parking'] },
      stay: {
        adults: 2,
        checkIn: '2025-09-22',
        checkOut: '2025-09-24',
        children: 1,
        infants: 1,
      },
    },
  };

  const result = createHotelsBeginCheckoutEventPayload(payload);

  const nullPromotionResult = createHotelsBeginCheckoutEventPayload({
    ...payload,
    quote: { ...payload.quote, offer: offerNullPromotion },
  });

  const noAvailableRoomsResult = createHotelsBeginCheckoutEventPayload({
    ...payload,
    ctaMessage: '',
    ctaMessageCategory: '',
  });

  const strikethroughResult = createHotelsBeginCheckoutEventPayload({
    ...payload,
    quote: {
      ...payload.quote,
      offer: {
        ...payload.quote.offer,
        charges: {
          ...payload.quote.offer.charges,
          strikethrough: { price: { amount: 300, currency: 'AUD' } },
        },
      },
    },
  });

  const stringStrikethroughAmountResult = createHotelsBeginCheckoutEventPayload({
    ...payload,
    quote: {
      ...payload.quote,
      offer: {
        ...payload.quote.offer,
        charges: {
          ...payload.quote.offer.charges,
          strikethrough: { price: { amount: '300', currency: 'AUD' } },
        },
      },
    },
  });

  it('creates a valid begin checkout event payload', () => {
    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.START,
        component_type: ComponentType.CHECKOUT,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        strikethrough_price: false,
      },
      ecommerce: {
        value: 200,
        currency: 'AUD',
        items: [
          {
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_category: 'hotel',
            item_variant: 'room1',
            index: 0,
            quantity: 2,
            price: 100,
            original_price: 100,
            cash_used: 200,
            points_used: 0,
            points_value: 0,
            has_offer: true,
            rating: 4.5,
            luxe: false,
            start_date: '2025-09-22',
            end_date: '2025-09-24',
            travellers_adult: 2,
            travellers_children: 1,
            travellers_infant: 1,
            number_of_nights: 2,
            exclusive_offer: false,
            points_earned: 1000,
            pay_in_points_percent: 0,
            offer_type: 'Save10',
            rebook: false,
            includes: 'breakfast, late check-in, parking',
            payment_type: 'credit card',
            location: 'Sydney, NSW, Australia',
            international_or_domestic: 'Domestic',
            strikethrough_price: false,
          },
        ],
      },
    });
  });

  it('creates a valid begin checkout event payload when promotion is null', () => {
    expect(nullPromotionResult).toStrictEqual({
      event_data: {
        action: ActionType.START,
        component_type: ComponentType.CHECKOUT,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        strikethrough_price: false,
      },
      ecommerce: {
        value: 200,
        currency: 'AUD',
        items: [
          {
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_category: 'hotel',
            item_variant: 'room1',
            index: 0,
            quantity: 2,
            price: 100,
            original_price: 100,
            cash_used: 200,
            points_used: 0,
            points_value: 0,
            has_offer: false,
            rating: 4.5,
            luxe: false,
            start_date: '2025-09-22',
            end_date: '2025-09-24',
            travellers_adult: 2,
            travellers_children: 1,
            travellers_infant: 1,
            number_of_nights: 2,
            exclusive_offer: false,
            points_earned: 1000,
            pay_in_points_percent: 0,
            offer_type: undefined,
            rebook: false,
            includes: 'breakfast, late check-in, parking',
            payment_type: 'credit card',
            location: 'Sydney, NSW, Australia',
            international_or_domestic: 'Domestic',
            strikethrough_price: false,
          },
        ],
      },
    });
  });

  it('creates a valid begin checkout event payload when there is no available rooms data', () => {
    expect(noAvailableRoomsResult).toStrictEqual({
      event_data: {
        action: ActionType.START,
        component_type: ComponentType.CHECKOUT,
        strikethrough_price: false,
      },
      ecommerce: {
        value: 200,
        currency: 'AUD',
        items: [
          {
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_category: 'hotel',
            item_variant: 'room1',
            index: 0,
            quantity: 2,
            price: 100,
            original_price: 100,
            cash_used: 200,
            points_used: 0,
            points_value: 0,
            has_offer: true,
            rating: 4.5,
            luxe: false,
            start_date: '2025-09-22',
            end_date: '2025-09-24',
            travellers_adult: 2,
            travellers_children: 1,
            travellers_infant: 1,
            number_of_nights: 2,
            exclusive_offer: false,
            points_earned: 1000,
            pay_in_points_percent: 0,
            offer_type: 'Save10',
            rebook: false,
            includes: 'breakfast, late check-in, parking',
            payment_type: 'credit card',
            location: 'Sydney, NSW, Australia',
            international_or_domestic: 'Domestic',
            strikethrough_price: false,
          },
        ],
      },
    });
  });

  it('creates a valid begin checkout event payload with strikethrough price', () => {
    expect(strikethroughResult).toStrictEqual({
      event_data: {
        action: ActionType.START,
        component_type: ComponentType.CHECKOUT,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        strikethrough_price: true,
      },
      ecommerce: {
        value: 200,
        currency: 'AUD',
        items: [
          {
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_category: 'hotel',
            item_variant: 'room1',
            index: 0,
            quantity: 2,
            price: 100,
            original_price: 100,
            cash_used: 200,
            points_used: 0,
            points_value: 0,
            has_offer: true,
            rating: 4.5,
            luxe: false,
            start_date: '2025-09-22',
            end_date: '2025-09-24',
            travellers_adult: 2,
            travellers_children: 1,
            travellers_infant: 1,
            number_of_nights: 2,
            exclusive_offer: false,
            points_earned: 1000,
            pay_in_points_percent: 0,
            offer_type: 'Save10',
            rebook: false,
            includes: 'breakfast, late check-in, parking',
            payment_type: 'credit card',
            location: 'Sydney, NSW, Australia',
            international_or_domestic: 'Domestic',
            strikethrough_price: true,
          },
        ],
      },
    });
  });

  it('creates a valid begin checkout event payload with strikethrough price even if the amount is string', () => {
    expect(stringStrikethroughAmountResult).toStrictEqual({
      event_data: {
        action: ActionType.START,
        component_type: ComponentType.CHECKOUT,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        strikethrough_price: true,
      },
      ecommerce: {
        value: 200,
        currency: 'AUD',
        items: [
          {
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_category: 'hotel',
            item_variant: 'room1',
            index: 0,
            quantity: 2,
            price: 100,
            original_price: 100,
            cash_used: 200,
            points_used: 0,
            points_value: 0,
            has_offer: true,
            rating: 4.5,
            luxe: false,
            start_date: '2025-09-22',
            end_date: '2025-09-24',
            travellers_adult: 2,
            travellers_children: 1,
            travellers_infant: 1,
            number_of_nights: 2,
            exclusive_offer: false,
            points_earned: 1000,
            pay_in_points_percent: 0,
            offer_type: 'Save10',
            rebook: false,
            includes: 'breakfast, late check-in, parking',
            payment_type: 'credit card',
            location: 'Sydney, NSW, Australia',
            international_or_domestic: 'Domestic',
            strikethrough_price: true,
          },
        ],
      },
    });
  });
});
