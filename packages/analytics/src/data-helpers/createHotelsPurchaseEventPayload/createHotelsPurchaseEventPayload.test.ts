import type { Quote } from '../hotelsUtils/types';
import cashBooking from '../../mocks/cash-booking.json';
import quote from '../../mocks/cash-quote.json';
import classicRewardsBooking from '../../mocks/classic-rewards-booking.json';
import pointsBooking from '../../mocks/points-booking.json';
import propertyPointsBooking from '../../mocks/property-points-booking.json';
import { createHotelsPurchaseEventPayload } from './createHotelsPurchaseEventPayload';

describe('createHotelsPurchaseEventPayload', () => {
  it('response is valid using cash', () => {
    const event = createHotelsPurchaseEventPayload({
      isRebooked: false,
      booking: cashBooking,
      quote,
      ctaMessage: 'Hurry, we only have 1 left!',
      ctaMessageCategory: 'available rooms',
    });
    expect(event).toEqual(
      expect.objectContaining({
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
          cta_message: 'Hurry, we only have 1 left!',
          cta_message_category: 'available rooms',
          strikethrough_price: false,
        },
        ecommerce: {
          transaction_id: 'e5b7c172-c459-42d8-a4a7-94c264b302e9',
          value: 90.0,
          tax: 8.18,
          shipping: 0,
          payment_type: 'credit card',
          deposit_pay_percentage: 0,
          total_cash_used: 90,
          total_points_earned: 540,
          total_points_used_percentage: 0,
          total_points_used: 0,
          total_points_value: 11494,
          voucher_value: 0,
          discount_value: 0,
          voucher_used: false,
          currency: 'AUD',
          coupon: undefined,
          items: [
            expect.objectContaining({
              item_id: '858016',
              item_name: 'Oakwood Premier Melbourne',
              item_category: 'apartments',
              item_variant: 'Deluxe Room',
              item_offer: 'Members Exclusive - Save 10% plus double points',
              index: 0,
              price: 90,
              original_price: 755.92,
              cash_used: 90,
              points_used: 0,
              has_offer: true,
              rating: 5,
              luxe: false,
              start_date: '2024-08-20',
              end_date: '2024-08-21',
              travellers_adult: 2,
              travellers_children: 0,
              travellers_infant: 0,
              number_of_nights: 1,
              number_of_rooms: 1,
              exclusive_offer: true,
              points_earned: 540,
              pay_in_points_percentage: 0,
              offer_type: 'Exclusive Deal',
              rebook: false,
              includes:
                'Ensuite bathroom, Bathrobes, Desk, Bedding, Non-smoking, Private bathroom, Mirror, Radio, Housekeeping, Bathroom, Shower, Alarm clock, Hairdryer, Room service, Heating, Sheets, Air conditioning, In-room safe, TV, Towels, Wake-up calls, Complimentary toiletries, Slippers, Refrigerator, Iron and ironing board, Clock radio, Rainfall showerhead, Television channels, Coffee/Tea, Internet, Phone, Telephone, Espresso maker',
              payment_type: 'credit card',
              location: 'Victoria, Australia',
              international_or_domestic: 'Domestic',
              cta_message: 'Hurry, we only have 1 left!',
              cta_message_category: 'available rooms',
              strikethrough_price: false,
            }),
          ],
        },
      }),
    );
  });

  it('response is valid using points', () => {
    const event = createHotelsPurchaseEventPayload({
      isRebooked: false,
      booking: pointsBooking,
      quote,
      ctaMessage: 'Hurry, we only have 1 left!',
      ctaMessageCategory: 'available rooms',
    });

    expect(event).toEqual(
      expect.objectContaining({
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
          cta_message: 'Hurry, we only have 1 left!',
          cta_message_category: 'available rooms',
          strikethrough_price: false,
        },
        ecommerce: {
          transaction_id: '05a7096e-0d71-4bcf-80b6-52efe2e706bb',
          value: 90,
          tax: 8.18,
          shipping: 0,
          payment_type: 'points',
          deposit_pay_percentage: 0,
          total_cash_used: 0,
          total_points_earned: 540,
          total_points_used_percentage: '100.00',
          total_points_used: 11494,
          total_points_value: 11494,
          voucher_value: 0,
          discount_value: 0,
          voucher_used: false,
          currency: 'AUD',
          coupon: undefined,
          items: [
            expect.objectContaining({
              item_id: '858016',
              item_name: 'Oakwood Premier Melbourne',
              item_category: 'apartments',
              item_variant: 'Deluxe Room',
              item_offer: 'Members Exclusive - Save 10% plus double points',
              index: 0,
              price: 90,
              original_price: 755.92,
              cash_used: 0,
              points_used: 11494,
              has_offer: true,
              rating: 5,
              luxe: false,
              start_date: '2024-08-20',
              end_date: '2024-08-21',
              travellers_adult: 2,
              travellers_children: 0,
              travellers_infant: 0,
              number_of_nights: 1,
              number_of_rooms: 1,
              exclusive_offer: true,
              points_earned: 540,
              pay_in_points_percentage: '100.00',
              offer_type: 'Exclusive Deal',
              rebook: false,
              includes:
                'Ensuite bathroom, Bathrobes, Desk, Bedding, Non-smoking, Private bathroom, Mirror, Radio, Housekeeping, Bathroom, Shower, Alarm clock, Hairdryer, Room service, Heating, Sheets, Air conditioning, In-room safe, TV, Towels, Wake-up calls, Complimentary toiletries, Slippers, Refrigerator, Iron and ironing board, Clock radio, Rainfall showerhead, Television channels, Coffee/Tea, Internet, Phone, Telephone, Espresso maker',
              payment_type: 'points',
              location: 'Victoria, Australia',
              international_or_domestic: 'Domestic',
              cta_message: 'Hurry, we only have 1 left!',
              cta_message_category: 'available rooms',
              strikethrough_price: false,
            }),
          ],
        },
      }),
    );
  });

  it('response is valid for classic reward using points', () => {
    const event = createHotelsPurchaseEventPayload({
      isRebooked: false,
      booking: classicRewardsBooking,
      quote,
      ctaMessage: 'Hurry, we only have 1 left!',
      ctaMessageCategory: 'available rooms',
    });

    expect(event).toEqual(
      expect.objectContaining({
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
          cta_message: 'Hurry, we only have 1 left!',
          cta_message_category: 'available rooms',
          strikethrough_price: false,
        },
        ecommerce: {
          transaction_id: 'c8e1500d-ecd5-48fc-9bcd-2489d070f274',
          value: 470.4,
          tax: 42.76,
          shipping: 0,
          payment_type: 'points',
          deposit_pay_percentage: 0,
          total_cash_used: 0,
          total_points_earned: 0,
          total_points_used_percentage: '100.00',
          total_points_used: 62000,
          total_points_value: 62000,
          voucher_value: 0,
          discount_value: 0,
          voucher_used: false,
          currency: 'AUD',
          coupon: undefined,
          items: [
            expect.objectContaining({
              item_id: '710',
              item_name: 'Metro Aspire Hotel Sydney',
              item_category: 'hotels',
              item_variant: 'Premium Deluxe Room',
              item_offer: 'Classic Reward Offer',
              index: 0,
              price: 235.2,
              original_price: 377.96,
              cash_used: 0,
              points_used: 62000,
              has_offer: true,
              rating: 4,
              luxe: false,
              start_date: '2024-08-25',
              end_date: '2024-08-27',
              travellers_adult: 2,
              travellers_children: 0,
              travellers_infant: 0,
              number_of_nights: 2,
              number_of_rooms: 1,
              exclusive_offer: false,
              points_earned: 0,
              pay_in_points_percentage: '100.00',
              offer_type: 'Classic Reward',
              rebook: false,
              includes:
                'Balcony/patio, Desk, Opening windows, Electronic keys, Non-smoking, Private bathroom, Housekeeping, Hairdryer, Air conditioning, In-room safe, TV, Bath with shower, Complimentary toiletries, Sofa bed, Shower/tub combination, TV internet access, Iron and ironing board, Coffee/Tea, Internet, Cribs/infant beds, Telephone',
              payment_type: 'points',
              location: 'New South Wales, Australia',
              international_or_domestic: 'Domestic',
              cta_message: 'Hurry, we only have 1 left!',
              cta_message_category: 'available rooms',
              strikethrough_price: false,
            }),
          ],
        },
      }),
    );
  });

  it('response is valid using points when amount is payable at property', () => {
    const event = createHotelsPurchaseEventPayload({
      isRebooked: false,
      booking: propertyPointsBooking,
      quote,
      ctaMessage: 'Hurry, we only have 1 left!',
      ctaMessageCategory: 'available rooms',
    });

    expect(event).toEqual(
      expect.objectContaining({
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
          cta_message: 'Hurry, we only have 1 left!',
          cta_message_category: 'available rooms',
          strikethrough_price: false,
        },
        ecommerce: {
          transaction_id: '483d17d3-da10-40cc-a470-4c59478f68bd',
          value: 423.76,
          tax: 64.53,
          shipping: 0,
          payment_type: 'points',
          deposit_pay_percentage: 0,
          total_cash_used: 0,
          total_points_earned: 3807,
          total_points_used_percentage: '100.00',
          total_points_used: 53674,
          total_points_value: 53674,
          voucher_value: 0,
          discount_value: 0,
          voucher_used: false,
          currency: 'AUD',
          coupon: undefined,
          items: [
            expect.objectContaining({
              item_id: '19258',
              item_name: 'Hilton Hawaiian Village Waikiki Beach Resort',
              item_category: 'resorts',
              item_variant: 'Room, 2 Double Beds, Resort View',
              item_offer: 'Room, 2 Double Beds, Resort View - Non-refundable',
              index: 0,
              price: 423.76,
              original_price: 755.92,
              cash_used: 0,
              points_used: 53674,
              has_offer: false,
              rating: 4,
              luxe: false,
              start_date: '2024-10-01',
              end_date: '2024-10-02',
              travellers_adult: 2,
              travellers_children: 0,
              travellers_infant: 0,
              number_of_nights: 1,
              number_of_rooms: 1,
              exclusive_offer: false,
              points_earned: 3807,
              pay_in_points_percentage: '100.00',
              offer_type: undefined,
              rebook: false,
              includes:
                'Premium bedding, Coffee/tea maker, Hair dryer, MP3 docking station, LED light bulbs, Rollaway/extra beds (surcharge), Premium TV channels, Non-Smoking, WiFi speed - 250+ Mbps (good for 3–5 people or up to 10 devices), TV size: 55, Connecting/adjoining rooms available, Number of beds - 1, Lowered locks/deadbolt, Furnished lanai, Blackout drapes/curtains, Lowered peephole/view port in door, Designer toiletries, Free cribs/infant beds, Daily housekeeping, Iron/ironing board, Height-adjustable showerhead, Grab bar - in shower, Desk chair, Wireless internet access, Toothbrush and toothpaste available on request, In-room climate control (air conditioning), Hand-held showerhead, Refrigerator, Towels provided, DVD player, WiFi (surcharge), Wheelchair-accessible bathroom vanity, Phone, Laptop-friendly workspace, Closed captioned TV, TV size measurement: inch, Television, Wardrobe or closet, Bedsheets provided, Free local calls, In-room safe, Hypo-allergenic bedding available, Cable TV service, Visual fire alarm, Flat-panel TV, Telephone accessibility kit, Low-height bed, Free toiletries, Wheelchair-width doorways, Eco-friendly toiletries, Private bathroom, Desk, Shower/tub combination, Low-height desk, Low-height counters/sink, Room service (limited hours)',
              payment_type: 'points',
              location: 'HI, United States of America',
              international_or_domestic: 'International',
              cta_message: 'Hurry, we only have 1 left!',
              cta_message_category: 'available rooms',
              strikethrough_price: false,
            }),
          ],
        },
      }),
    );
  });

  it('response is valid using cash and there is no available rooms data', () => {
    const event = createHotelsPurchaseEventPayload({
      isRebooked: false,
      booking: cashBooking,
      quote,
      ctaMessage: '',
      ctaMessageCategory: '',
    });
    expect(event).toEqual(
      expect.objectContaining({
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
          strikethrough_price: false,
        },
        ecommerce: {
          transaction_id: 'e5b7c172-c459-42d8-a4a7-94c264b302e9',
          value: 90.0,
          tax: 8.18,
          shipping: 0,
          payment_type: 'credit card',
          deposit_pay_percentage: 0,
          total_cash_used: 90,
          total_points_earned: 540,
          total_points_used_percentage: 0,
          total_points_used: 0,
          total_points_value: 11494,
          voucher_value: 0,
          discount_value: 0,
          voucher_used: false,
          currency: 'AUD',
          coupon: undefined,
          items: [
            expect.objectContaining({
              item_id: '858016',
              item_name: 'Oakwood Premier Melbourne',
              item_category: 'apartments',
              item_variant: 'Deluxe Room',
              item_offer: 'Members Exclusive - Save 10% plus double points',
              index: 0,
              price: 90,
              original_price: 755.92,
              cash_used: 90,
              points_used: 0,
              has_offer: true,
              rating: 5,
              luxe: false,
              start_date: '2024-08-20',
              end_date: '2024-08-21',
              travellers_adult: 2,
              travellers_children: 0,
              travellers_infant: 0,
              number_of_nights: 1,
              number_of_rooms: 1,
              exclusive_offer: true,
              points_earned: 540,
              pay_in_points_percentage: 0,
              offer_type: 'Exclusive Deal',
              rebook: false,
              includes:
                'Ensuite bathroom, Bathrobes, Desk, Bedding, Non-smoking, Private bathroom, Mirror, Radio, Housekeeping, Bathroom, Shower, Alarm clock, Hairdryer, Room service, Heating, Sheets, Air conditioning, In-room safe, TV, Towels, Wake-up calls, Complimentary toiletries, Slippers, Refrigerator, Iron and ironing board, Clock radio, Rainfall showerhead, Television channels, Coffee/Tea, Internet, Phone, Telephone, Espresso maker',
              payment_type: 'credit card',
              location: 'Victoria, Australia',
              international_or_domestic: 'Domestic',
              strikethrough_price: false,
            }),
          ],
        },
      }),
    );
  });

  it('response is valid using cash and strikethrough pricing is available', () => {
    const strikethroughQuote: Quote = {
      ...quote,
      offer: {
        ...quote.offer,
        charges: {
          ...quote.offer.charges,
          strikethrough: { price: { amount: 300, currency: 'AUD' } },
        },
      },
    };
    const event = createHotelsPurchaseEventPayload({
      isRebooked: false,
      booking: cashBooking,
      quote: strikethroughQuote,
      ctaMessage: '',
      ctaMessageCategory: '',
    });
    expect(event).toEqual(
      expect.objectContaining({
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
          strikethrough_price: true,
        },
        ecommerce: {
          transaction_id: 'e5b7c172-c459-42d8-a4a7-94c264b302e9',
          value: 90.0,
          tax: 8.18,
          shipping: 0,
          payment_type: 'credit card',
          deposit_pay_percentage: 0,
          total_cash_used: 90,
          total_points_earned: 540,
          total_points_used_percentage: 0,
          total_points_used: 0,
          total_points_value: 11494,
          voucher_value: 0,
          discount_value: 0,
          voucher_used: false,
          currency: 'AUD',
          coupon: undefined,
          items: [
            expect.objectContaining({
              item_id: '858016',
              item_name: 'Oakwood Premier Melbourne',
              item_category: 'apartments',
              item_variant: 'Deluxe Room',
              item_offer: 'Members Exclusive - Save 10% plus double points',
              index: 0,
              price: 90,
              original_price: 755.92,
              cash_used: 90,
              points_used: 0,
              has_offer: true,
              rating: 5,
              luxe: false,
              start_date: '2024-08-20',
              end_date: '2024-08-21',
              travellers_adult: 2,
              travellers_children: 0,
              travellers_infant: 0,
              number_of_nights: 1,
              number_of_rooms: 1,
              exclusive_offer: true,
              points_earned: 540,
              pay_in_points_percentage: 0,
              offer_type: 'Exclusive Deal',
              rebook: false,
              includes:
                'Ensuite bathroom, Bathrobes, Desk, Bedding, Non-smoking, Private bathroom, Mirror, Radio, Housekeeping, Bathroom, Shower, Alarm clock, Hairdryer, Room service, Heating, Sheets, Air conditioning, In-room safe, TV, Towels, Wake-up calls, Complimentary toiletries, Slippers, Refrigerator, Iron and ironing board, Clock radio, Rainfall showerhead, Television channels, Coffee/Tea, Internet, Phone, Telephone, Espresso maker',
              payment_type: 'credit card',
              location: 'Victoria, Australia',
              international_or_domestic: 'Domestic',
              strikethrough_price: true,
            }),
          ],
        },
      }),
    );
  });

  it('response is valid using cash and strikethrough pricing is available even if the amount is string', () => {
    const strikethroughQuote: Quote = {
      ...quote,
      offer: {
        ...quote.offer,
        charges: {
          ...quote.offer.charges,
          strikethrough: { price: { amount: '300', currency: 'AUD' } },
        },
      },
    };
    const event = createHotelsPurchaseEventPayload({
      isRebooked: false,
      booking: cashBooking,
      quote: strikethroughQuote,
      ctaMessage: '',
      ctaMessageCategory: '',
    });
    expect(event).toEqual(
      expect.objectContaining({
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
          strikethrough_price: true,
        },
        ecommerce: {
          transaction_id: 'e5b7c172-c459-42d8-a4a7-94c264b302e9',
          value: 90.0,
          tax: 8.18,
          shipping: 0,
          payment_type: 'credit card',
          deposit_pay_percentage: 0,
          total_cash_used: 90,
          total_points_earned: 540,
          total_points_used_percentage: 0,
          total_points_used: 0,
          total_points_value: 11494,
          voucher_value: 0,
          discount_value: 0,
          voucher_used: false,
          currency: 'AUD',
          coupon: undefined,
          items: [
            expect.objectContaining({
              item_id: '858016',
              item_name: 'Oakwood Premier Melbourne',
              item_category: 'apartments',
              item_variant: 'Deluxe Room',
              item_offer: 'Members Exclusive - Save 10% plus double points',
              index: 0,
              price: 90,
              original_price: 755.92,
              cash_used: 90,
              points_used: 0,
              has_offer: true,
              rating: 5,
              luxe: false,
              start_date: '2024-08-20',
              end_date: '2024-08-21',
              travellers_adult: 2,
              travellers_children: 0,
              travellers_infant: 0,
              number_of_nights: 1,
              number_of_rooms: 1,
              exclusive_offer: true,
              points_earned: 540,
              pay_in_points_percentage: 0,
              offer_type: 'Exclusive Deal',
              rebook: false,
              includes:
                'Ensuite bathroom, Bathrobes, Desk, Bedding, Non-smoking, Private bathroom, Mirror, Radio, Housekeeping, Bathroom, Shower, Alarm clock, Hairdryer, Room service, Heating, Sheets, Air conditioning, In-room safe, TV, Towels, Wake-up calls, Complimentary toiletries, Slippers, Refrigerator, Iron and ironing board, Clock radio, Rainfall showerhead, Television channels, Coffee/Tea, Internet, Phone, Telephone, Espresso maker',
              payment_type: 'credit card',
              location: 'Victoria, Australia',
              international_or_domestic: 'Domestic',
              strikethrough_price: true,
            }),
          ],
        },
      }),
    );
  });
});
