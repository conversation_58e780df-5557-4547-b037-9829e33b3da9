import { Decimal } from 'decimal.js';

import type { Booking, Quote } from '../hotelsUtils/types';
import { ActionType, ComponentType } from '../../enums';
import { cashToPoints } from '../../utils/cash-to-points';
import { getPaymentType } from '../../utils/payment-type';
import { getPointsTotals } from '../../utils/points-totals';
import {
  calculateDailyPreDiscountAmount,
  calculateDailyTotalAmount,
  calculateTotalNights,
  createLocationString,
  isPromotionExclusive,
  isPropertyLuxury,
} from '../hotelsUtils';

export interface CreateHotelsPurchaseEventPayloadOptions {
  booking: Booking;
  ctaMessage?: string | null;
  ctaMessageCategory?: string | null;
  isRebooked: boolean;
  quote: Quote;
}

export interface CreateHotelsPurchaseEventPayloadReturn {
  ecommerce: {
    coupon: string | null | undefined;
    currency: string;
    deposit_pay_percentage: number;
    discount_value: number;
    items: [
      {
        cash_used: number;
        cta_message?: string | null;
        cta_message_category?: string | null;
        end_date: string;
        exclusive_offer: boolean | undefined;
        has_offer: boolean;
        includes: string;
        index: number;
        international_or_domestic: string;
        item_category: string;
        item_id: string;
        item_name: string;
        item_offer: string;
        item_variant: string;
        location: string;
        luxe: boolean | undefined;
        number_of_nights: number;
        number_of_rooms: number;
        offer_type: string | undefined;
        original_price: number;
        pay_in_points_percentage: string | number;
        payment_type: string;
        points_earned: number;
        points_used: number;
        price: number;
        rating: number;
        rebook: boolean;
        start_date: string;
        strikethrough_price: boolean;
        travellers_adult: number;
        travellers_children: number;
        travellers_infant: number;
      },
    ];
    payment_type: string;
    shipping: number;
    tax: number;
    total_cash_used: number;
    total_points_earned: number;
    total_points_used: number;
    total_points_used_percentage: string | number;
    total_points_value: number;
    transaction_id: string;
    value: number;
    voucher_used: boolean;
    voucher_value: number;
  };
  event_data: {
    action: ActionType;
    component_type: ComponentType;
    cta_message?: string | null;
    cta_message_category?: string | null;
    strikethrough_price: boolean;
  };
}

export const createHotelsPurchaseEventPayload = ({
  booking,
  quote,
  isRebooked,
  ctaMessage,
  ctaMessageCategory,
}: CreateHotelsPurchaseEventPayloadOptions): CreateHotelsPurchaseEventPayloadReturn => {
  const methods = {
    creditCard: new Decimal(booking.bookingTotal.creditCard.total ?? 0),

    creditNote: new Decimal(booking.bookingTotal.creditNote.total ?? 0),

    points: new Decimal(booking.bookingTotal.points.totalPoints ?? 0),

    qantasGroupCreditVoucher: new Decimal(booking.bookingTotal.qantasGroupCreditVoucher.total ?? 0),

    voucher: new Decimal(booking.bookingTotal.voucher.total ?? 0),
  };

  const totalAmount = new Decimal(booking.reservation.offer.charges.payableAtBooking.total.amount);

  const discountAmount = new Decimal(quote.offer.charges.payableAtBooking.discount.amount);

  const preDiscountTotal = new Decimal(quote.offer.charges.totalBeforeDiscount.amount);

  const { pointsPayPercentage } = getPointsTotals(totalAmount, methods);

  const isDepositPay = !!quote.offer.paidByDeposit;
  const depositPayPercentage = isDepositPay
    ? methods.creditCard.dividedBy(totalAmount).times(100).round().toNumber()
    : 0;

  const totalNights = calculateTotalNights(
    booking.reservation.checkIn,
    booking.reservation.checkOut,
  );

  const { city, state, country } = booking.reservation.property.address;
  const location = createLocationString(city, state, country);

  const hasStrikethroughPrice = Number(quote.offer.charges.strikethrough?.price.amount) > 0;

  return {
    event_data: {
      action: ActionType.PURCHASE,
      component_type: ComponentType.CHECKOUT,
      ...(ctaMessage && { cta_message: ctaMessage }),
      ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
      strikethrough_price: hasStrikethroughPrice,
    },
    ecommerce: {
      transaction_id: booking.id,
      currency: booking.reservation.offer.charges.total.currency,
      value: totalAmount.toNumber(),
      tax: Number(booking.reservation.offer.charges.payableAtBooking.taxDisplayable.amount),
      shipping: 0,
      total_cash_used: methods.creditCard.toNumber(),
      deposit_pay_percentage: depositPayPercentage,
      total_points_earned: booking.reservation.offer.pointsEarned?.qffPoints.total ?? 0,
      total_points_used_percentage: pointsPayPercentage,
      total_points_used: methods.points.toNumber(),
      total_points_value:
        booking.value.currency === 'PTS'
          ? Number(booking.value.amount)
          : cashToPoints(totalAmount, booking.reservation.offer.pointsTierInstance).toNumber(),
      payment_type: getPaymentType(methods, isDepositPay),
      voucher_value: methods.voucher.toNumber(),
      discount_value: Number(discountAmount),
      coupon: booking.reservation.offer.promotion?.promotionCode,
      voucher_used: !!methods.voucher.toNumber(),
      items: [
        {
          item_id: booking.reservation.property.id,
          item_name: booking.reservation.property.name,
          item_category: booking.reservation.property.category,
          item_variant: booking.reservation.roomType.name,
          item_offer: booking.reservation.offer.name,
          index: 0,
          price: calculateDailyTotalAmount(totalAmount, totalNights),
          original_price: calculateDailyPreDiscountAmount(preDiscountTotal, totalNights),
          cash_used: methods.creditCard.toNumber(),
          points_used: methods.points.toNumber(),
          points_earned: booking.reservation.offer.pointsEarned?.qffPoints.total ?? 0,
          pay_in_points_percentage: pointsPayPercentage,
          rating: Number(booking.reservation.property.rating ?? 0),
          start_date: booking.reservation.checkIn.toString(),
          end_date: booking.reservation.checkOut.toString(),
          travellers_adult: booking.reservation.adults,
          travellers_children: booking.reservation.children,
          travellers_infant: booking.reservation.infants,
          number_of_nights: totalNights,
          number_of_rooms: 1,
          includes: booking.reservation.roomType.roomTypeFacilities.join(', '),
          payment_type: getPaymentType(methods, isDepositPay),
          location,
          international_or_domestic: country === 'Australia' ? 'Domestic' : 'International',
          luxe: isPropertyLuxury(booking.reservation.offer.promotion?.name ?? undefined),
          exclusive_offer: isPromotionExclusive(
            booking.reservation.offer.promotion?.name ?? undefined,
          ),
          rebook: isRebooked,
          has_offer: !!booking.reservation.offer.promotion,
          offer_type: booking.reservation.offer.promotion?.name ?? undefined,
          strikethrough_price: hasStrikethroughPrice,
          ...(ctaMessage && { cta_message: ctaMessage }),
          ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
        },
      ],
    },
  };
};
