import type { CreateHotelsViewItemEventPayloadOptions } from './createHotelsViewItemEventPayload';
import { ComponentType } from '../../enums';
import { createHotelsViewItemEventPayload } from './createHotelsViewItemEventPayload';

describe('createHotelsViewItemEventPayload', () => {
  const payload: CreateHotelsViewItemEventPayloadOptions = {
    adults: 2,
    availableOffers: 5,
    availableRooms: 3,
    averagePointsEarned: 1000,
    averagePrice: 200,
    averagePriceBeforeDiscount: 250,
    averagePricePoints: 1000,
    checkIn: '2025-10-01',
    checkOut: '2025-10-02',
    children: 0,
    ctaMessage: 'Hu<PERSON>, we only have 1 left!',
    ctaMessageCategory: 'available rooms',
    currency: 'AUD',
    infants: 0,
    location: 'Sydney',
    luxuryOffer: true,
    payWith: 'cash',
    rebook: false,
    recommendedProperty: true,
    results: {
      property: {
        category: 'hotel',
        facilities: 'breakfast, late checkin, parking',
        id: '12345',
        imageCount: 10,
        internationalOrDomestic: 'domestic',
        name: 'Hotel XYZ',
        rating: 4.5,
      },
    },
  };

  const payloadWithoutAvailableRoomsMessages = {
    ...payload,
    ctaMessage: '',
    ctaMessageCategory: '',
  };

  const payloadWithStrikethrough = {
    ...payload,
    strikethrough: {
      price: {
        amount: 17900,
        currency: 'AUD',
      },
    },
  };

  const payloadWithStrikethroughAmountString = {
    ...payload,
    strikethrough: {
      price: {
        amount: '17900',
        currency: 'AUD',
      },
    },
  };

  const result = createHotelsViewItemEventPayload(payload);
  const noAvailableRoomsResult = createHotelsViewItemEventPayload(
    payloadWithoutAvailableRoomsMessages,
  );
  const strikethroughResult = createHotelsViewItemEventPayload(payloadWithStrikethrough);
  const stringStrikethroughAmountResult = createHotelsViewItemEventPayload(
    payloadWithStrikethroughAmountString,
  );

  it('creates a valid view item payload', () => {
    expect(result).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            available_offer_count: 5,
            available_room_count: 3,
            cash_used: undefined,
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            end_date: '2025-10-02',
            image_count: 10,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: undefined,
            item_variant: undefined,
            location: 'Sydney',
            luxe: true,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: undefined,
            original_price: 250,
            pay_in_points_percentage: undefined,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: undefined,
            points_value: 1000,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            recommend: true,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: false,
          },
        ],
        value: 200,
      },
      event_data: {
        action: 'view',
        component_type: ComponentType.ITEM,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        strikethrough_price: false,
      },
    });
  });

  it('creates a valid view item payload when there is no available rooms data', () => {
    expect(noAvailableRoomsResult).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            available_offer_count: 5,
            available_room_count: 3,
            cash_used: undefined,
            end_date: '2025-10-02',
            image_count: 10,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: undefined,
            item_variant: undefined,
            location: 'Sydney',
            luxe: true,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: undefined,
            original_price: 250,
            pay_in_points_percentage: undefined,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: undefined,
            points_value: 1000,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            recommend: true,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: false,
          },
        ],
        value: 200,
      },
      event_data: {
        action: 'view',
        component_type: ComponentType.ITEM,
        strikethrough_price: false,
      },
    });
  });

  it('creates a valid view item payload when there is strikethrough', () => {
    expect(strikethroughResult).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            available_offer_count: 5,
            available_room_count: 3,
            cash_used: undefined,
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            end_date: '2025-10-02',
            image_count: 10,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: undefined,
            item_variant: undefined,
            location: 'Sydney',
            luxe: true,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: undefined,
            original_price: 250,
            pay_in_points_percentage: undefined,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: undefined,
            points_value: 1000,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            recommend: true,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: true,
          },
        ],
        value: 200,
      },
      event_data: {
        action: 'view',
        component_type: ComponentType.ITEM,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        strikethrough_price: true,
      },
    });
  });

  it('creates a valid view item payload when there is strikethrough pricing in string', () => {
    expect(stringStrikethroughAmountResult).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            available_offer_count: 5,
            available_room_count: 3,
            cash_used: undefined,
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            end_date: '2025-10-02',
            image_count: 10,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: undefined,
            item_variant: undefined,
            location: 'Sydney',
            luxe: true,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: undefined,
            original_price: 250,
            pay_in_points_percentage: undefined,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: undefined,
            points_value: 1000,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            recommend: true,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: true,
          },
        ],
        value: 200,
      },
      event_data: {
        action: 'view',
        component_type: ComponentType.ITEM,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        strikethrough_price: true,
      },
    });
  });
});
