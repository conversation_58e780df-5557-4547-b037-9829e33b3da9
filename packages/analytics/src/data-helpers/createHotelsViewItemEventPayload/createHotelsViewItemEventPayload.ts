import { ActionType, ComponentType } from '../../enums';
import { formatDate, totalNights } from '../hotelsUtils/hotelsUtils';

export interface CreateHotelsViewItemEventPayloadOptions {
  adults: number;
  availableOffers: number;
  availableRooms: number;
  averagePointsEarned: number;
  averagePrice: number;
  averagePriceBeforeDiscount: number;
  averagePricePoints: number;
  checkIn: string | Date;
  checkOut: string | Date;
  children: number;
  ctaMessage?: string | null;
  ctaMessageCategory?: string | null;
  currency: string;
  infants: number;
  location: string;
  luxuryOffer: boolean;
  payWith: 'cash' | 'points' | 'cash and points';
  rebook: boolean;
  recommendedProperty: boolean;
  results: {
    property: {
      category: string;
      facilities: string;
      id: string;
      imageCount: number;
      internationalOrDomestic: 'domestic' | 'international';
      name: string;
      rating: number;
    };
  };
  strikethrough?: { price: { amount: string | number; currency: string } };
}

export interface CreateHotelsViewItemEventPayloadReturn {
  ecommerce: {
    currency: string;
    items: [
      {
        available_offer_count: number;
        available_room_count: number;
        cash_used: number | undefined;
        cta_message?: string | null;
        cta_message_category?: string | null;
        end_date: string;
        image_count: number;
        includes: string;
        index: number;
        international_or_domestic: string;
        item_category: string;
        item_id: string;
        item_name: string;
        item_offer: string | undefined;
        item_variant: string | undefined;
        location: string;
        luxe: boolean;
        number_of_nights: number;
        number_of_rooms: number;
        offer_type: string | undefined;
        original_price: number;
        pay_in_points_percentage: number | undefined;
        payment_type: string;
        points_earned: number;
        points_used: number | undefined;
        points_value: number;
        price: number;
        quantity: number;
        rating: number;
        rebook: boolean;
        recommend: boolean;
        start_date: string;
        strikethrough_price: boolean;
        travellers_adult: number;
        travellers_children: number;
        travellers_infant: number;
      },
    ];
    value: number;
  };
  event_data: {
    action: string;
    component_type: string;
    cta_message?: string | null;
    cta_message_category?: string | null;
    strikethrough_price: boolean;
  };
}

export const createHotelsViewItemEventPayload = ({
  adults,
  availableOffers,
  availableRooms,
  averagePointsEarned,
  averagePrice,
  averagePriceBeforeDiscount,
  averagePricePoints,
  checkIn,
  checkOut,
  children,
  ctaMessage,
  ctaMessageCategory,
  currency,
  infants,
  location,
  luxuryOffer,
  payWith,
  rebook,
  recommendedProperty,
  results,
  strikethrough,
}: CreateHotelsViewItemEventPayloadOptions): CreateHotelsViewItemEventPayloadReturn => {
  const startDate = formatDate(new Date(checkIn));
  const endDate = formatDate(new Date(checkOut));
  const numberOfNights = totalNights(checkIn, checkOut);
  const hasStrikethroughPrice = Number(strikethrough?.price.amount) > 0;

  return {
    event_data: {
      action: ActionType.VIEW,
      component_type: ComponentType.ITEM,
      ...(ctaMessage && { cta_message: ctaMessage }),
      ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
      strikethrough_price: hasStrikethroughPrice,
    },
    ecommerce: {
      value: averagePrice,
      currency,
      items: [
        {
          item_id: results.property.id,
          item_name: results.property.name,
          item_category: results.property.category,
          item_variant: undefined,
          item_offer: undefined,
          index: 0,
          quantity: 1,
          price: averagePrice,
          original_price: averagePriceBeforeDiscount,
          cash_used: undefined,
          points_used: undefined,
          points_value: averagePricePoints,
          points_earned: averagePointsEarned,
          pay_in_points_percentage: undefined,
          rating: results.property.rating,
          start_date: startDate,
          end_date: endDate,
          travellers_adult: adults,
          travellers_children: children,
          travellers_infant: infants,
          number_of_nights: numberOfNights,
          number_of_rooms: 1,
          includes: results.property.facilities,
          payment_type: payWith,
          location,
          international_or_domestic: results.property.internationalOrDomestic,
          luxe: luxuryOffer,
          recommend: recommendedProperty,
          rebook,
          offer_type: undefined,
          available_offer_count: availableOffers,
          available_room_count: availableRooms,
          image_count: results.property.imageCount,
          ...(ctaMessage && { cta_message: ctaMessage }),
          ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
          strikethrough_price: hasStrikethroughPrice,
        },
      ],
    },
  };
};
