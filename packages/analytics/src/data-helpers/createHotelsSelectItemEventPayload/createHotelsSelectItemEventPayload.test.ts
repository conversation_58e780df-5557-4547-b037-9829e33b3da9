import type { CreateHotelsSelectItemEventPayloadOptions } from './createHotelsSelectItemEventPayload';
import { PaymentType, SearchCategory, SearchType } from '../../enums';
import { createHotelsSelectItemEventPayload } from './createHotelsSelectItemEventPayload';

const payload: CreateHotelsSelectItemEventPayloadOptions = {
  listName: 'Hotels in Melbourne, VIC, Australia',
  location: 'Melbourne, VIC, Australia',
  type: SearchType.LIST,
  category: SearchCategory.QANTAS,
  ctaMessage: 'available rooms',
  ctaMessageCategory: 'We only have 1 left at this price!',
  payWith: PaymentType.CASH,
  availableProperties: 368,
  property: {
    id: '17186',
    name: 'Lane way by Ovolo',
    category: 'hotels',
    propertyFacilities: [
      'Luggage storage',
      'Reception (24 hour)',
      'Porter / Bell staff',
      'Non-smoking property',
      'Lifts',
      'WiFi (free)',
      'Airport transfers (surcharge)',
      'Guest laundry',
      'Dry cleaning / Laundry service',
      'Onsite parking (surcharge)',
      'Accessible facilities',
    ],
  },
  roomType: { name: 'Downtown Studio' },
  query: {
    checkIn: new Date('2024-05-26T00:00:00+10:00'),
    checkOut: new Date('2024-05-27T00:00:00+10:00'),
    adults: 2,
    children: 0,
    infants: 0,
  },
  pointsConversion: {
    levels: [
      { min: 0, max: 150, rate: 0.00824 },
      { min: 150, max: 400, rate: 0.00834 },
      { min: 400, max: 650, rate: 0.00848 },
      { min: 650, max: 900, rate: 0.00875 },
      { min: 900, max: null, rate: 0.00931 },
    ],
    name: 'VERSION11',
  },
  offer: {
    name: 'OTA Flexible Rate 48HR CXL – Room Only',
    charges: {
      total: { amount: '299.00', currency: 'AUD' },
    },
    depositPay: {
      depositPayable: false,
    },
    promotion: { name: 'Featured' },
  },
};

const payloadWithStrikethrough = {
  ...payload,
  offer: {
    ...payload.offer,
    charges: {
      total: { amount: '299', currency: 'AUD' },
      strikethrough: {
        price: {
          amount: 179000,
          currency: 'AUD',
        },
      },
    },
  },
};

const payloadWithStrikethroughAmountString = {
  ...payload,
  offer: {
    ...payload.offer,
    charges: {
      total: { amount: '299', currency: 'AUD' },
      strikethrough: {
        price: {
          amount: '179000',
          currency: 'AUD',
        },
      },
    },
  },
};

const payloadWithoutAvailableRoomsMessages = {
  ...payload,
  ctaMessage: '',
  ctaMessageCategory: '',
};

const result = createHotelsSelectItemEventPayload(payload);
const noAvailableRoomsResult = createHotelsSelectItemEventPayload(
  payloadWithoutAvailableRoomsMessages,
);
const strikethroughResult = createHotelsSelectItemEventPayload(payloadWithStrikethrough);
const stringStrikethroughAmountResult = createHotelsSelectItemEventPayload(
  payloadWithStrikethroughAmountString,
);

describe('createHotelsSelectItemEventPayload', () => {
  it('creates a valid select item event payload', () => {
    expect(result).toStrictEqual({
      event_data: {
        action: 'click',
        component_type: 'item_list',
        search_term: 'Melbourne, VIC, Australia',
        search_type: SearchType.LIST,
        search_category: SearchCategory.QANTAS,
        search_payment_toggle: PaymentType.CASH,
        available_property_count: 368,
        start_date: '2024-05-26',
        end_date: '2024-05-27',
        travellers_adult: 2,
        travellers_children: 0,
        travellers_infant: 0,
        cta_message: 'available rooms',
        cta_message_category: 'We only have 1 left at this price!',
        strikethrough_price: false,
      },
      ecommerce: {
        currency: 'AUD',
        item_list_id: 'hotels_in_melbourne_vic_australia',
        item_list_name: 'Hotels in Melbourne, VIC, Australia',
        items: [
          {
            item_id: '17186',
            item_name: 'Lane way by Ovolo',
            item_category: 'hotels',
            item_variant: 'Downtown Studio',
            index: 0,
            quantity: 1,
            price: 299,
            points_value: 36070,
            has_offer: true,
            has_deposit_pay: false,
            cta_message: 'available rooms',
            cta_message_category: 'We only have 1 left at this price!',
            strikethrough_price: false,
          },
        ],
      },
    });
  });

  it('creates a valid select item event payload when there is no available rooms data', () => {
    expect(noAvailableRoomsResult).toStrictEqual({
      event_data: {
        action: 'click',
        component_type: 'item_list',
        search_term: 'Melbourne, VIC, Australia',
        search_type: SearchType.LIST,
        search_category: SearchCategory.QANTAS,
        search_payment_toggle: PaymentType.CASH,
        available_property_count: 368,
        start_date: '2024-05-26',
        end_date: '2024-05-27',
        travellers_adult: 2,
        travellers_children: 0,
        travellers_infant: 0,
        strikethrough_price: false,
      },
      ecommerce: {
        currency: 'AUD',
        item_list_id: 'hotels_in_melbourne_vic_australia',
        item_list_name: 'Hotels in Melbourne, VIC, Australia',
        items: [
          {
            item_id: '17186',
            item_name: 'Lane way by Ovolo',
            item_category: 'hotels',
            item_variant: 'Downtown Studio',
            index: 0,
            quantity: 1,
            price: 299,
            points_value: 36070,
            has_offer: true,
            has_deposit_pay: false,
            strikethrough_price: false,
          },
        ],
      },
    });
  });

  it('creates a valid select item event payload when there is strikethrough pricing', () => {
    expect(strikethroughResult).toStrictEqual({
      event_data: {
        action: 'click',
        component_type: 'item_list',
        search_term: 'Melbourne, VIC, Australia',
        search_type: SearchType.LIST,
        search_category: SearchCategory.QANTAS,
        search_payment_toggle: PaymentType.CASH,
        available_property_count: 368,
        start_date: '2024-05-26',
        end_date: '2024-05-27',
        travellers_adult: 2,
        travellers_children: 0,
        travellers_infant: 0,
        cta_message: 'available rooms',
        cta_message_category: 'We only have 1 left at this price!',
        strikethrough_price: true,
      },
      ecommerce: {
        currency: 'AUD',
        item_list_id: 'hotels_in_melbourne_vic_australia',
        item_list_name: 'Hotels in Melbourne, VIC, Australia',
        items: [
          {
            item_id: '17186',
            item_name: 'Lane way by Ovolo',
            item_category: 'hotels',
            item_variant: 'Downtown Studio',
            index: 0,
            quantity: 1,
            price: 299,
            points_value: 36070,
            has_offer: true,
            cta_message: 'available rooms',
            cta_message_category: 'We only have 1 left at this price!',
            has_deposit_pay: false,
            strikethrough_price: true,
          },
        ],
      },
    });
  });

  it('creates a valid select item event payload when there is strikethrough pricing amount in string', () => {
    expect(stringStrikethroughAmountResult).toStrictEqual({
      event_data: {
        action: 'click',
        component_type: 'item_list',
        search_term: 'Melbourne, VIC, Australia',
        search_type: SearchType.LIST,
        search_category: SearchCategory.QANTAS,
        search_payment_toggle: PaymentType.CASH,
        available_property_count: 368,
        start_date: '2024-05-26',
        end_date: '2024-05-27',
        travellers_adult: 2,
        travellers_children: 0,
        travellers_infant: 0,
        cta_message: 'available rooms',
        cta_message_category: 'We only have 1 left at this price!',
        strikethrough_price: true,
      },
      ecommerce: {
        currency: 'AUD',
        item_list_id: 'hotels_in_melbourne_vic_australia',
        item_list_name: 'Hotels in Melbourne, VIC, Australia',
        items: [
          {
            item_id: '17186',
            item_name: 'Lane way by Ovolo',
            item_category: 'hotels',
            item_variant: 'Downtown Studio',
            index: 0,
            quantity: 1,
            price: 299,
            points_value: 36070,
            has_offer: true,
            cta_message: 'available rooms',
            cta_message_category: 'We only have 1 left at this price!',
            has_deposit_pay: false,
            strikethrough_price: true,
          },
        ],
      },
    });
  });
});
