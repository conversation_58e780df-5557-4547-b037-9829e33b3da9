import type { PaymentType, SearchCategory, SearchType } from '../../enums';
import type { Offer, PointsConversion } from '../hotelsUtils/types';
import { ComponentType } from '../../enums';
import { ActionType } from '../../enums/action-type';
import {
  calculateId,
  calculateTotalPointsValueAnyPayWith,
  formatDate,
  getTotalPrice,
  totalNights,
} from '../hotelsUtils/hotelsUtils';

type DateString = string | Date;

export interface CreateHotelsSelectItemEventPayloadOptions {
  availableProperties?: number;
  availableRooms?: number;
  category: SearchCategory;
  ctaMessage?: string | null;
  ctaMessageCategory?: string | null;
  listName: string;
  location: string;
  offer: Offer;
  payWith: PaymentType;
  pointsConversion: PointsConversion;
  property: {
    category: string;
    id: string;
    name: string;
    propertyFacilities: string[];
  };
  query: {
    adults: number;
    checkIn: DateString;
    checkOut: DateString;
    children: number;
    infants: number;
  };
  roomType?: {
    name?: string;
  };
  type: SearchType;
}

export interface CreateHotelsSelectItemEventPayloadReturn {
  ecommerce: {
    currency: string;
    item_list_id: string;
    item_list_name: string;
    items: {
      cta_message?: string | null;
      cta_message_category?: string | null;
      has_deposit_pay: boolean;
      has_offer: boolean;
      index: number;
      item_category: string;
      item_id: string;
      item_name: string;
      item_variant?: string;
      points_value?: number;
      price: number;
      quantity?: number;
      strikethrough_price: boolean;
    }[];
  };
  event_data: {
    action: ActionType.CLICK;
    available_property_count?: number;
    available_room_count?: number;
    component_type: ComponentType;
    cta_message?: string | null;
    cta_message_category?: string | null;
    end_date?: string;
    search_category: SearchCategory;
    search_payment_toggle: PaymentType;
    search_term: string;
    search_type: SearchType;
    start_date?: string;
    strikethrough_price: boolean;
    travellers_adult: number;
    travellers_children: number;
    travellers_infant: number;
  };
}

export const createHotelsSelectItemEventPayload = ({
  availableProperties,
  availableRooms,
  category,
  listName,
  location,
  offer,
  payWith,
  pointsConversion,
  property,
  query,
  roomType,
  type,
  ctaMessage,
  ctaMessageCategory,
}: CreateHotelsSelectItemEventPayloadOptions): CreateHotelsSelectItemEventPayloadReturn => {
  const numberOfNights = totalNights(query.checkIn, query.checkOut);
  const totalPointsValue = calculateTotalPointsValueAnyPayWith(offer.charges, pointsConversion);
  const totalPrice = getTotalPrice(offer.charges);
  const hasStrikethroughPrice = Number(offer.charges.strikethrough?.price.amount) > 0;
  const id = calculateId(listName);
  const startDate = formatDate(new Date(query.checkIn));
  const endDate = formatDate(new Date(query.checkOut));

  return {
    event_data: {
      action: ActionType.CLICK,
      component_type: ComponentType.ITEM_LIST,
      search_term: location,
      search_type: type,
      search_category: category,
      search_payment_toggle: payWith,
      ...(availableProperties && {
        available_property_count: availableProperties,
      }),
      ...(availableRooms && {
        available_room_count: availableRooms,
      }),
      start_date: startDate,
      end_date: endDate,
      travellers_adult: query.adults,
      travellers_children: query.children,
      travellers_infant: query.infants,
      ...(ctaMessage && { cta_message: ctaMessage }),
      ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
      strikethrough_price: hasStrikethroughPrice,
    },
    ecommerce: {
      currency: offer.charges.totalCash?.currency ?? offer.charges.total.currency,
      item_list_id: id,
      item_list_name: listName,
      items: [
        {
          item_id: property.id,
          item_name: property.name,
          item_category: property.category,
          ...(roomType?.name && { item_variant: roomType.name }),
          index: 0,
          quantity: numberOfNights,
          price: totalPrice,
          points_value: totalPointsValue,
          has_offer: !!offer.promotion,
          has_deposit_pay: offer.depositPay.depositPayable,
          ...(ctaMessage && { cta_message: ctaMessage }),
          ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
          strikethrough_price: hasStrikethroughPrice,
        },
      ],
    },
  };
};
