import { ComponentType, PageType } from '../../enums';
import { createHotelsAddToCartEventPayload } from './createHotelsAddToCartEventPayload';

describe('createHotelsAddToCartEventPayload', () => {
  const offer = {
    charges: {
      total: { amount: '200', currency: 'AUD' },
      totalBeforeDiscount: { amount: '200', currency: 'AUD' },
      totalCash: { amount: '200', currency: 'AUD' },
    },
    name: 'offer1',
    pointsEarned: {
      qffPoints: { total: 1000 },
    },
    promotion: {
      name: 'promotion1',
      promotionCode: 'promo1',
    },
  };

  const offerNullPromotion = { ...offer, promotion: null };
  const offerWithStrikethroughPrice = {
    ...offer,
    strikethrough: {
      price: {
        amount: 17900,
        currency: 'AUD',
      },
    },
  };

  const offerWithStrikethroughAmountString = {
    ...offer,
    strikethrough: {
      price: {
        amount: '17900',
        currency: 'AUD',
      },
    },
  };

  const payload = {
    ctaMessage: 'Hurry, we only have 1 left!',
    ctaMessageCategory: 'available rooms',
    initialCash: 200,
    isRebooked: false,
    offer,
    pointsConversion: {
      levels: [
        {
          min: 0,
          max: 250,
          rate: 0.00824,
        },
      ] as [{ max: number; min: number; rate: number }],
      name: 'VERSION11',
    },
    property: {
      address: { city: 'Sydney', country: 'Australia', state: 'NSW' },
      category: 'hotel',
      id: '12345',
      name: 'Hotel XYZ',
      rating: 4.5,
    },
    query: { adults: 2, checkIn: '2025-10-01', checkOut: '2025-10-02', children: 0, infants: 0 },
    roomType: { name: 'room1', roomTypeFacilities: ['breakfast', 'late checkin', 'parking'] },
  };

  const result = createHotelsAddToCartEventPayload(payload);

  const nullPromotionResult = createHotelsAddToCartEventPayload({
    ...payload,
    ...{ offer: offerNullPromotion },
  });

  const noAvailableRoomsResult = createHotelsAddToCartEventPayload({
    ...payload,
    ctaMessage: '',
    ctaMessageCategory: '',
  });

  const strikethroughResult = createHotelsAddToCartEventPayload({
    ...payload,
    ...{ offer: offerWithStrikethroughPrice },
  });

  const stringStrikethroughAmountResult = createHotelsAddToCartEventPayload({
    ...payload,
    ...{ offer: offerWithStrikethroughAmountString },
  });

  it('creates a valid add to cart payload when all fields are populated', () => {
    expect(result).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            cash_used: 200,
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            end_date: '2025-10-02',
            exclusive_offer: false,
            has_offer: true,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'Domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: 'offer1',
            item_variant: 'room1',
            location: 'Sydney, NSW, Australia',
            luxe: false,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: 'promotion1',
            original_price: 200,
            pay_in_points_percentage: 0,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: 0,
            points_value: 24272,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: false,
          },
        ],
        value: 200,
      },
      event_data: {
        component_type: ComponentType.CART,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        location: PageType.PROPERTY,
        strikethrough_price: false,
      },
    });
  });

  it('creates a valid add to cart payload when promotion is null', () => {
    expect(nullPromotionResult).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            cash_used: 200,
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            end_date: '2025-10-02',
            exclusive_offer: false,
            has_offer: false,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'Domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: 'offer1',
            item_variant: 'room1',
            location: 'Sydney, NSW, Australia',
            luxe: false,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: undefined,
            original_price: 200,
            pay_in_points_percentage: 0,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: 0,
            points_value: 24272,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: false,
          },
        ],
        value: 200,
      },
      event_data: {
        component_type: ComponentType.CART,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        location: PageType.PROPERTY,
        strikethrough_price: false,
      },
    });
  });

  it('creates a valid add to cart payload when there is no available rooms data', () => {
    expect(noAvailableRoomsResult).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            cash_used: 200,
            end_date: '2025-10-02',
            exclusive_offer: false,
            has_offer: true,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'Domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: 'offer1',
            item_variant: 'room1',
            location: 'Sydney, NSW, Australia',
            luxe: false,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: 'promotion1',
            original_price: 200,
            pay_in_points_percentage: 0,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: 0,
            points_value: 24272,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: false,
          },
        ],
        value: 200,
      },
      event_data: {
        component_type: ComponentType.CART,
        location: PageType.PROPERTY,
        strikethrough_price: false,
      },
    });
  });

  it('creates a valid add to cart payload when all fields are populated and strikethrough is provided', () => {
    expect(strikethroughResult).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            cash_used: 200,
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            end_date: '2025-10-02',
            exclusive_offer: false,
            has_offer: true,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'Domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: 'offer1',
            item_variant: 'room1',
            location: 'Sydney, NSW, Australia',
            luxe: false,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: 'promotion1',
            original_price: 200,
            pay_in_points_percentage: 0,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: 0,
            points_value: 24272,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: false,
          },
        ],
        value: 200,
      },
      event_data: {
        component_type: ComponentType.CART,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        location: PageType.PROPERTY,
        strikethrough_price: false,
      },
    });
  });

  it('creates a valid add to cart payload when all fields are populated and strikethrough is provided in string amount', () => {
    expect(stringStrikethroughAmountResult).toStrictEqual({
      ecommerce: {
        currency: 'AUD',
        items: [
          {
            cash_used: 200,
            cta_message: 'Hurry, we only have 1 left!',
            cta_message_category: 'available rooms',
            end_date: '2025-10-02',
            exclusive_offer: false,
            has_offer: true,
            includes: 'breakfast, late checkin, parking',
            index: 0,
            international_or_domestic: 'Domestic',
            item_category: 'hotel',
            item_id: '12345',
            item_name: 'Hotel XYZ',
            item_offer: 'offer1',
            item_variant: 'room1',
            location: 'Sydney, NSW, Australia',
            luxe: false,
            number_of_nights: 1,
            number_of_rooms: 1,
            offer_type: 'promotion1',
            original_price: 200,
            pay_in_points_percentage: 0,
            payment_type: 'cash',
            points_earned: 1000,
            points_used: 0,
            points_value: 24272,
            price: 200,
            quantity: 1,
            rating: 4.5,
            rebook: false,
            start_date: '2025-10-01',
            travellers_adult: 2,
            travellers_children: 0,
            travellers_infant: 0,
            strikethrough_price: false,
          },
        ],
        value: 200,
      },
      event_data: {
        component_type: ComponentType.CART,
        cta_message: 'Hurry, we only have 1 left!',
        cta_message_category: 'available rooms',
        location: PageType.PROPERTY,
        strikethrough_price: false,
      },
    });
  });
});
