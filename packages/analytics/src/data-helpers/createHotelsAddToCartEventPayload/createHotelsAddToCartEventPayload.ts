import { Decimal } from 'decimal.js';

import type { PointsConversion } from '../hotelsUtils/types';
import { ComponentType, PageType } from '../../enums';
import { cashToPoints } from '../../utils/cash-to-points';
import { getPaymentType } from '../../utils/payment-type';
import {
  calculateDailyPreDiscountAmount,
  calculateDailyTotalAmount,
  calculatePointsPaidPercentage,
  formatDate,
  isPromotionExclusive,
  isPropertyLuxury,
  totalInCashBeforeDiscount,
  totalNights,
} from '../hotelsUtils/hotelsUtils';

export interface CreateHotelsAddToCartEventPayloadOptions {
  ctaMessage?: string | null;
  ctaMessageCategory?: string | null;
  initialCash: number;
  isRebooked: boolean;
  offer: {
    charges: {
      strikethrough?: { price: { amount: string; currency: string } };
      total: { amount: string; currency: string };
      totalBeforeDiscount: { amount: string; currency: string };
      totalCash?: { amount: string; currency: string };
    };
    name: string;
    pointsEarned?: {
      qffPoints: { total: number };
    };
    promotion?: {
      name?: string | null;
      promotionCode?: string | null;
    } | null;
  };
  pointsConversion: PointsConversion;
  property: {
    address: { city: string; country: string; state: string };
    category: string;
    id: string;
    name: string;
    rating: number;
  };
  query: {
    adults: number;
    checkIn: string | Date;
    checkOut: string | Date;
    children: number;
    infants: number;
  };
  roomType: { name: string; roomTypeFacilities?: string[] };
}

export interface CreateHotelsAddToCartEventPayloadReturn {
  ecommerce: {
    currency: string;
    items: [
      {
        cash_used: number;
        cta_message?: string | null;
        cta_message_category?: string | null;
        end_date: string;
        exclusive_offer: boolean | undefined;
        has_offer: boolean;
        includes: string;
        index: number;
        international_or_domestic: string;
        item_category: string;
        item_id: string;
        item_name: string;
        item_offer: string;
        item_variant: string;
        location: string;
        luxe: boolean | undefined;
        number_of_nights: number;
        number_of_rooms: number;
        offer_type: string | undefined;
        original_price: number;
        pay_in_points_percentage: number;
        payment_type: string;
        points_earned: number;
        points_used: number;
        points_value: number;
        price: number;
        quantity: number;
        rating: number;
        rebook: boolean;
        start_date: string;
        strikethrough_price: boolean;
        travellers_adult: number;
        travellers_children: number;
        travellers_infant: number;
      },
    ];
    value: number;
  };
  event_data: {
    component_type: ComponentType;
    cta_message?: string | null;
    cta_message_category?: string | null;
    location: PageType;
    strikethrough_price: boolean;
  };
}

export const createHotelsAddToCartEventPayload = ({
  property,
  offer,
  roomType,
  query,
  initialCash,
  pointsConversion,
  isRebooked,
  ctaMessage,
  ctaMessageCategory,
}: CreateHotelsAddToCartEventPayloadOptions): CreateHotelsAddToCartEventPayloadReturn => {
  const startDate = formatDate(new Date(query.checkIn));
  const endDate = formatDate(new Date(query.checkOut));

  const { amount: totalInCash, currency: totalCurrency } =
    offer.charges.total.currency === 'PTS' ? (offer.charges.totalCash ?? {}) : offer.charges.total;

  const { city, state, country } = property.address;
  const location = `${city ? `${city}, ` : ''}${state ? `${state}, ` : ''}${country}`;
  const numberOfNights = totalNights(query.checkIn, query.checkOut);

  const price = totalInCash ? calculateDailyTotalAmount(totalInCash, numberOfNights) : 0;

  const cashBeforeDiscount = totalInCashBeforeDiscount(
    offer.charges.totalBeforeDiscount.currency === 'PTS',
    offer.charges.totalBeforeDiscount.amount,
    pointsConversion,
  );

  const cashUsed =
    offer.charges.total.currency === 'PTS' ? initialCash : offer.charges.total.amount;

  const pointsUsed =
    offer.charges.total.currency === 'PTS'
      ? new Decimal(offer.charges.total.amount).minus(
          cashToPoints(new Decimal(initialCash), pointsConversion),
        )
      : new Decimal(0);

  const hasStrikethroughPrice = Number(offer.charges.strikethrough?.price.amount) > 0;

  return {
    event_data: {
      component_type: ComponentType.CART,
      location: PageType.PROPERTY,
      ...(ctaMessage && { cta_message: ctaMessage }),
      ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
      strikethrough_price: hasStrikethroughPrice,
    },
    ecommerce: {
      value: Number(totalInCash),
      currency: totalCurrency ?? 'AUD',
      items: [
        {
          item_id: property.id,
          item_name: property.name,
          item_category: property.category,
          item_variant: roomType.name,
          item_offer: offer.name,
          index: 0,
          quantity: numberOfNights,
          price,
          original_price: calculateDailyPreDiscountAmount(cashBeforeDiscount, numberOfNights),
          cash_used: Number(cashUsed),
          points_used: Number(pointsUsed),
          points_value: totalInCash
            ? cashToPoints(new Decimal(totalInCash), pointsConversion).toNumber()
            : 0,
          points_earned: Number(offer.pointsEarned?.qffPoints.total),
          pay_in_points_percentage: calculatePointsPaidPercentage(
            pointsUsed,
            offer.charges.total.amount,
          ),
          rating: property.rating,
          start_date: startDate,
          end_date: endDate,
          travellers_adult: query.adults,
          travellers_children: query.children,
          travellers_infant: query.infants,
          number_of_nights: numberOfNights,
          number_of_rooms: 1,
          includes: roomType.roomTypeFacilities?.join(', ') ?? '',
          payment_type: getPaymentType({
            cash: new Decimal(cashUsed),
            points: new Decimal(pointsUsed),
          }),
          location,
          international_or_domestic: country === 'Australia' ? 'Domestic' : 'International',
          luxe: isPropertyLuxury(offer.promotion?.name ?? undefined),
          exclusive_offer: isPromotionExclusive(offer.promotion?.name ?? undefined),
          rebook: isRebooked,
          has_offer: !!offer.promotion?.name,
          offer_type: offer.promotion?.name ?? undefined,
          ...(ctaMessage && { cta_message: ctaMessage }),
          ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
          strikethrough_price: hasStrikethroughPrice,
        },
      ],
    },
  };
};
